{"id": 48, "name": "ebay测试高级任务", "url": "https://www.ebay.com", "links": "https://www.ebay.com", "containJudge": false, "desc": "https://www.ebay.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.ebay.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "https://www.ebay.com"}, {"id": 1, "name": "inputText_1", "nodeName": "输入文字", "nodeId": 2, "desc": "要输入的文本，如京东搜索框输入：电脑", "type": "string", "exampleValue": "iphone", "value": "iphone"}], "outputParameters": [{"id": 0, "name": "参数2_文本", "desc": "", "type": "string", "exampleValue": "NewListing"}, {"id": 1, "name": "参数1_文本", "desc": "", "type": "string", "exampleValue": "AppleiPhone13mini128GBUnlocked-Excellent"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 6, 2, 3, 4], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.ebay.com", "links": "https://www.ebay.com", "scrollType": 0, "scrollCount": 0}}, {"id": 3, "index": 2, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 4, "tabIndex": 0, "useLoop": false, "xpath": "//*[@id=\"gh-ac\"]", "wait": 0, "beforeJS": "arguments[0].value = \"123123\"", "beforeJSWaitTime": 0, "afterJS": "arguments[0].value = \"iPhone Pro\"", "afterJSWaitTime": 0, "value": "iphone", "allXPaths": ["/html/body/header[1]/table[1]/tbody[1]/tr[1]/td[5]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[1]/div[1]/input[1]", "//input[contains(., '')]", "id(\"gh-ac\")", "//INPUT[@class='gh-tb ui-autocomplete-input ui-autocomplete-loading']", "//INPUT[@name='_nkw']"]}}, {"id": 4, "index": 3, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 4, "tabIndex": 0, "useLoop": false, "xpath": "//*[@id=\"gh-btn\"]", "wait": 0, "beforeJS": "arguments[0].value= \"ttt\"", "beforeJSWaitTime": 0, "afterJS": "arguments[0].value= \"tt2t\"", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 0, "params": [], "allXPaths": ["/html/body/header[1]/table[1]/tbody[1]/tr[1]/td[5]/form[1]/table[1]/tbody[1]/tr[1]/td[3]/input[1]", "//input[contains(., '')]", "id(\"gh-btn\")", "//INPUT[@class='btn btn-prim gh-spr']"]}}, {"id": 5, "index": 4, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [5], "isInLoop": false, "position": 4, "parameters": {"history": 5, "tabIndex": 0, "useLoop": false, "xpath": "/html/body/div[5]/div[4]/div[2]/div[1]/div[2]/ul[1]/li/div[1]/div[2]/a[1]/div[1]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 0, "loopType": 1, "pathList": "", "textList": "", "exitCount": 0, "historyWait": 2, "allXPaths": ["/html/body/div[5]/div[4]/div[2]/div[1]/div[2]/ul[1]/li[2]/div[1]/div[2]/a[1]/div[1]", "//div[contains(., 'Apple iPho')]", "//DIV[@class='s-item__title']"]}}, {"id": 6, "index": 5, "parentId": 5, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 9, "relative": true, "name": "参数2_文本", "desc": "", "relativeXPath": "/span[1]/span[1]", "allXPaths": ["/span[1]/span[1]", "//span[contains(., 'New Listin')]", "//SPAN[@class='LIGHT_HIGHLIGHT']"], "exampleValues": [{"num": 4, "value": "NewListing"}, {"num": 6, "value": "NewListing"}, {"num": 10, "value": "NewListing"}, {"num": 17, "value": "NewListing"}, {"num": 23, "value": "AppleiPhone4s-8/16/32/64GBBlack-White"}, {"num": 42, "value": "NewListing"}, {"num": 59, "value": "NewListing"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "return arguments[0].innerText + \"人民币\"", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数1_文本", "desc": "", "relativeXPath": "/span[1]", "allXPaths": ["/span[1]", "//span[contains(., 'Apple iPho')]"], "exampleValues": [{"num": 0, "value": "AppleiPhone13mini128GBUnlocked-Excellent"}, {"num": 1, "value": "AppleiPhoneSE2ndGen2020A227564GB128GB256GB-UnlockedSmartphone-Good"}, {"num": 2, "value": "FullyWorkingAppleiphone2g1stgenerationunlocked4GB8GB16GBRareIOS1.0"}, {"num": 3, "value": "New&SealedAppleiPhone416GBA1332UnlockedWhiteBlackSmartPhone"}, {"num": 4, "value": "AppleiPhone12ProMax-128GB-Gold(Unlocked)(CA)"}, {"num": 5, "value": "AppleiPhone13-256GB-Midnight"}, {"num": 6, "value": "NEWUnlockedAppleiPhone13128GBStarlight-CLEANESN-MDMBypassed-READ-"}, {"num": 7, "value": "AppleiPhone14ProMaxA16128GB256GB512GB1TBUnlockedNewSealed"}, {"num": 8, "value": "AppleiPhone1stGeneration8GB-Black(AT&T)A1203(GSM)TestedFullworking"}, {"num": 9, "value": "800USD!!AppleiPhone14ProMax-1TB-DeepPurpleSpaceBlack(Unlocked)"}, {"num": 10, "value": "AppleiPhone13ProMax-128GB-SierraBlue(Unlocked)"}, {"num": 11, "value": "AppleiPhone11-64GB-Black(T-Mobile)A2111(CDMA+GSM)"}, {"num": 12, "value": "AppleiPhone14ProMax-256GB-DeepPurple(Unlocked)"}, {"num": 13, "value": "AppleiPhone12mini-128GB-Black(Unlocked)"}, {"num": 14, "value": "Workingverywell,AppleiPhone3GS8GB16GB32GBunlocked3GSmartphone"}, {"num": 15, "value": "AppleIphone11Pro128gbI-ClockedGREY,OKAYCONDITION,Perfectscreen"}, {"num": 16, "value": "OriginalUnlockedAppleiPhone4S-8/16/32/64GBiOS93GWIFISmartphone"}, {"num": 17, "value": "AppleiPhoneX-64GB-Silverwhite(Unlocked)A1865(CDMA+GSM)AAAconition"}, {"num": 18, "value": "AppleiPhoneXRUnlockedVariousColors64GB128GB256GBSmartphoneUsed"}, {"num": 19, "value": "AppleiPhone11Pro,ProMaxUnlocked64GB256GB512GBSmartphoneUsed"}, {"num": 20, "value": "AppleiPhone2GGeneration-8GB-Silver"}, {"num": 21, "value": "FullworkingOrignalAppleiphone1st2nd3rdGen2G3G3GS4/8/16/32Unlocked"}, {"num": 22, "value": "AppleiPhone14-128GB-Starlight(Unlocked)"}, {"num": 23, "value": ""}, {"num": 24, "value": "AppleiPhone5-32GB-Black<PERSON>hite(Unlocked)A1428(GSM)IOSSmartphone"}, {"num": 25, "value": "AppleiPhone1stGeneration-4GB8GB16GB-Black(Unlocked)A1203(GSM)"}, {"num": 26, "value": "AppleiPhone3GS-32GB-Black(Unlocked)A1303(GSM)"}, {"num": 27, "value": "📱AppleiPhone4S8/16/32GB-UnlockedBlackwhiteGradeACondition📱IOS6"}, {"num": 28, "value": "New&SealedAppleiPhone432GBA1332UnlockedWhiteBlackSmartPhone"}, {"num": 29, "value": "100%BrandNewOriginalAppleiPhone4S8/16/32/64GBIOS6IOS9Unlocked3G"}, {"num": 30, "value": "AppleiPhone4s8/16/32/64GBA1387(CDMA+GSM)IOS6.3.1IOS9.3.6UNLOCKED"}, {"num": 31, "value": "AppleiPhoneX64GB256GB-GreySilver-UNLOCKED-ExcellentGradeACondition"}, {"num": 32, "value": "lotofiphone4,6,8,8,andSE2020*READDISCRIPTON*"}, {"num": 33, "value": "ShockproofFRONT+BACKCaseCoverForiPhone14111213PROMAXXXRXS78"}, {"num": 34, "value": "AppleiPhone12-64GB-Black(Unlocked)"}, {"num": 35, "value": "AppleiPhone6s-128GB-SpaceGray(Verizon)A1688(FingerprintDisabledNewScreen)"}, {"num": 36, "value": "AppleiPhone11ProMaxUnlockedSmartphone,64GB,256GB,512GBSYDNEYSTOCK"}, {"num": 37, "value": "AppleiPhoneXUnlockedOLEDSmartphone5,8\",64GB,256GB,512GB,SYDNEYSTOCK"}, {"num": 38, "value": "FullWorkingAppleiPhone1stGeneration2G-8GB-Black(Unlocked)A1203(GSM)"}, {"num": 39, "value": "CaseForiPhone14ProMax14Pro14Plus14ShockproofSiliconeCover"}, {"num": 40, "value": "AppleiPhone864GBUNLOCKED"}, {"num": 41, "value": "originalAppleiPhone1stGeneration16GBunlocked2GGSMworkgoodIOS3"}, {"num": 42, "value": "AppleiPhone11ProMax-64GB-Silver(Unlocked)A2161(CDMA+GSM)NOFACEID"}, {"num": 43, "value": "AppleiPhone7A177832/128/256GBAT&TT-MobileGSMUnlockedGood"}, {"num": 44, "value": "AppleiPhoneXR-64GB-Black(Unlocked)A1984(CDMA+GSM)"}, {"num": 45, "value": "📱AppleiPhone516/32/64GB-UnlockedBlackwhiteGradeAConditionIOS10📱"}, {"num": 46, "value": "AppleiPhone3rdgeneration3GS-8GB16GB32GB-BlackWhite(Unlocked)phone"}, {"num": 47, "value": "Iphone11excellentcondition!64gbt-mobile"}, {"num": 48, "value": "AppleiPhone12mini-64GB-Black(Unlocked)"}, {"num": 49, "value": "AppleiPhone3GS-8GB-Black(AT&T)A1303(GSM)FastShipExcellentUsed"}, {"num": 50, "value": "AppleiPhone5c-8/16/32GB-ALLCOLORSUnlocked/AT&T/T-MobileA1532"}, {"num": 51, "value": "originalAppleiPhone2rdGeneration8GBunlocked2GGSMnetworkworkgood"}, {"num": 52, "value": "iPhoneXRunlocked128gbused"}, {"num": 53, "value": "AppleiPhone11ProUnlockedSmartphone,64GB,256GB,512GBSYDNEYSTOCK"}, {"num": 54, "value": "AppleiPhone4s-8/16/32/64GB-ALLCOLORSUnlocked/AT&T/SprintA1387"}, {"num": 55, "value": "AppleiPhone14ProMax-1TB-SpaceBlack(Unlocked)"}, {"num": 56, "value": "AppleiPhone8,UnlockedSmartphone,Black,Silver,Gold,Red-SYDNEYSTOCK"}, {"num": 57, "value": "AppleiPhone12miniUnlockedSmartphone,64GB,128GB,256GB-SYDNEYSTOCK"}, {"num": 58, "value": "5DPrivacyTemperedGlassScreenProtectorForiPhone111412PROMAX13Cover"}, {"num": 59, "value": "AppleiPhone14ProMax-512GB-SpaceBlack(Unlocked)"}], "default": "", "beforeJS": "arguments[0].innerText = arguments[0].innerText + \"美元\"", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "arguments[0].innerText = arguments[0].innerText + \"美元1\"", "afterJSWaitTime": 0}], "loopType": 1}}, {"id": 2, "index": 6, "parentId": 0, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "codeMode": "1", "code": "fasdfdasf", "waitTime": 0, "recordASField": 0}}]}