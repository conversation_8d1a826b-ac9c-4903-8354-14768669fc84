{"id": 39, "name": "百度文库", "url": "https://wenku.baidu.com/view/98593fe25ff7ba0d4a7302768e9951e79b896989.html?fr=hp_Database&_wkts_=1684099456020", "links": "https://wenku.baidu.com/view/98593fe25ff7ba0d4a7302768e9951e79b896989.html?fr=hp_Database&_wkts_=1684099456020", "containJudge": false, "desc": "https://wenku.baidu.com/view/98593fe25ff7ba0d4a7302768e9951e79b896989.html?fr=hp_Database&_wkts_=1684099456020", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://wenku.baidu.com/view/98593fe25ff7ba0d4a7302768e9951e79b896989.html?fr=hp_Database&_wkts_=1684099456020", "desc": "要采集的网址列表,多行以\\n分开", "type": "string", "exampleValue": "https://wenku.baidu.com/view/98593fe25ff7ba0d4a7302768e9951e79b896989.html?fr=hp_Database&_wkts_=1684099456020"}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "type": "string", "exampleValue": ""}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 10, "url": "https://wenku.baidu.com/view/98593fe25ff7ba0d4a7302768e9951e79b896989.html?fr=hp_Database&_wkts_=1684099456020", "links": "https://wenku.baidu.com/view/98593fe25ff7ba0d4a7302768e9951e79b896989.html?fr=hp_Database&_wkts_=1684099456020", "scrollType": 0, "scrollCount": 0}}, {"id": 2, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [3], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//canvas", "wait": 0, "scrollType": 0, "scrollCount": 0, "loopType": 1, "pathList": "", "textList": "", "exitCount": 0, "historyWait": 2}}, {"id": 3, "index": 3, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "params": [{"nodeType": 0, "contentType": 8, "relative": true, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "", "exampleValues": [{"num": 0, "value": ""}, {"num": 1, "value": ""}], "default": ""}], "loopType": 1}}]}