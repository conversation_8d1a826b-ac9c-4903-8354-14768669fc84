{"id": 35, "name": "测试", "url": "https://www.jd.com", "links": "https://www.jd.com", "containJudge": false, "desc": "https://www.jd.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.jd.com", "desc": "要采集的网址列表,多行以\\n分开", "type": "string", "exampleValue": "https://www.jd.com"}], "outputParameters": [{"id": 0, "name": "参数1_背景图片地址", "desc": "", "type": "string", "exampleValue": "url(\"https://i.ebayimg.com/images/g/jyoAAOSwVl1kUFr-/s-l200.webp\")"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 4, 2, 3], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "url": "https://www.jd.com", "links": "https://www.jd.com", "scrollType": 0, "scrollCount": 0}}, {"id": 3, "index": 2, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "params": [{"nodeType": 0, "contentType": 4, "relative": false, "name": "参数1_背景图片地址", "desc": "", "relativeXPath": "/html/body/div[6]/div[5]/ul[1]/li[1]/a[1]/div[1]/div[1]", "exampleValues": [{"num": 0, "value": "url(\"https://i.ebayimg.com/images/g/jyoAAOSwVl1kUFr-/s-l200.webp\")"}], "default": ""}]}}, {"id": 4, "index": 3, "parentId": 0, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "codeMode": 0, "code": "alert(\"My name is naib<PERSON>\")"}}, {"id": 2, "index": 4, "parentId": 0, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "codeMode": "1", "code": "ping www.baidu.com"}}]}