{"id": 6, "name": "OCR", "url": "https://www.ebay.com", "links": "https://www.ebay.com", "create_time": "5/25/2023, 9:11:47 PM", "version": "0.3.1", "containJudge": false, "desc": "https://www.ebay.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "Open Page", "value": "https://www.ebay.com", "desc": "List of URLs to be collected, separated by \\n for multiple lines", "type": "string", "exampleValue": "https://www.ebay.com"}], "outputParameters": [{"id": 0, "name": "para1_text", "desc": "", "type": "string", "exampleValue": "Home"}, {"id": 1, "name": "para2_text", "desc": "", "type": "string", "exampleValue": "Home"}, {"id": 2, "name": "para3_link_text", "desc": "", "type": "string", "exampleValue": "Saved"}, {"id": 3, "name": "para4_link_address", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/feed"}, {"id": 4, "name": "para5_text", "desc": "", "type": "string", "exampleValue": "Expand:Motors"}, {"id": 5, "name": "para6_text", "desc": "", "type": "string", "exampleValue": "Parts&Accessories"}, {"id": 6, "name": "para7_link_text", "desc": "", "type": "string", "exampleValue": "All Parts & Accessories"}, {"id": 7, "name": "para8_link_address", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/b/Auto-Parts-Accessories/6028/bn_569479"}, {"id": 8, "name": "para9_link_text", "desc": "", "type": "string", "exampleValue": "Car & Truck Parts"}, {"id": 9, "name": "para10_link_address", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/b/Car-Truck-Parts/6030/bn_562630"}, {"id": 10, "name": "para11_link_text", "desc": "", "type": "string", "exampleValue": "Motorcycle Parts"}, {"id": 11, "name": "para12_link_address", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/b/Motorcycle-Parts/10063/bn_557636"}, {"id": 12, "name": "para13_link_text", "desc": "", "type": "string", "exampleValue": "Automotive Tools & Supplies"}, {"id": 13, "name": "para14_link_address", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/b/Automotive-Tools-Supplies/34998/bn_1865501"}, {"id": 14, "name": "para15_link_text", "desc": "", "type": "string", "exampleValue": "Apparel & Merchandise"}, {"id": 15, "name": "para16_link_address", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/b/Motors-Apparel-Merchandise/6747/bn_583691"}, {"id": 16, "name": "para17_link_text", "desc": "", "type": "string", "exampleValue": "Motors Deals"}, {"id": 17, "name": "para18_link_address", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/deals/automotive"}, {"id": 18, "name": "para19_link_text", "desc": "", "type": "string", "exampleValue": "My Garage"}, {"id": 19, "name": "para20_link_address", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/g/mygarage"}, {"id": 20, "name": "para21_text", "desc": "", "type": "string", "exampleValue": "Vehicles"}, {"id": 21, "name": "para22_link_text", "desc": "", "type": "string", "exampleValue": "Cars & Trucks"}, {"id": 22, "name": "para23_link_address", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/b/Cars-and-Trucks/6001/bn_1865117"}, {"id": 23, "name": "para24_link_text", "desc": "", "type": "string", "exampleValue": "Classics"}, {"id": 24, "name": "para25_link_address", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/b/Classics/bn_7005623268"}, {"id": 25, "name": "para26_link_text", "desc": "", "type": "string", "exampleValue": "Motorcycles"}, {"id": 26, "name": "para27_link_address", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/b/Motorcycles/6024/bn_1865434"}, {"id": 27, "name": "para28_link_text", "desc": "", "type": "string", "exampleValue": "Powersports"}, {"id": 28, "name": "para29_link_address", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/b/Powersport-Vehicles/66466/bn_1865239"}, {"id": 29, "name": "para30_link_text", "desc": "", "type": "string", "exampleValue": "RVs & Campers"}, {"id": 30, "name": "para31_link_address", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/b/RVs-and-Campers/50054/bn_16581882"}, {"id": 31, "name": "para32_link_text", "desc": "", "type": "string", "exampleValue": "Trailers & Other Vehicles"}, {"id": 32, "name": "para33_link_address", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/b/Other-Vehicles-Trailers/6737/bn_16581863"}, {"id": 33, "name": "para34_link_text", "desc": "", "type": "string", "exampleValue": "Boats"}, {"id": 34, "name": "para35_link_address", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/b/Boats/26429/bn_1865510"}, {"id": 35, "name": "para36_link_text", "desc": "", "type": "string", "exampleValue": "Surveillance & Smart Home Electronics"}, {"id": 36, "name": "para37_link_address", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/b/Surveillance-Smart-Home-Electronics/185067/bn_115028425"}, {"id": 37, "name": "para38_link_text", "desc": "", "type": "string", "exampleValue": "Laptops & Netbooks"}, {"id": 38, "name": "para39_link_address", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/b/Laptops-Netbooks/175672/bn_1648276"}, {"id": 39, "name": "para40_link_text", "desc": "", "type": "string", "exampleValue": "Motors"}, {"id": 40, "name": "para41_link_address", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/b/Auto-Parts-and-Vehicles/6000/bn_1865334"}, {"id": 41, "name": "para42_link_text", "desc": "", "type": "string", "exampleValue": "Electronics"}, {"id": 42, "name": "para43_link_address", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/b/Electronics/bn_7000259124"}, {"id": 43, "name": "para44_link_text", "desc": "", "type": "string", "exampleValue": "Collectibles"}, {"id": 44, "name": "para45_link_address", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/b/Collectibles-Art/bn_7000259855"}, {"id": 45, "name": "para46_link_text", "desc": "", "type": "string", "exampleValue": "Home & Garden"}, {"id": 46, "name": "para47_link_address", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/b/Home-Garden/11700/bn_1853126"}, {"id": 47, "name": "para48_link_text", "desc": "", "type": "string", "exampleValue": "Fashion"}, {"id": 48, "name": "para49_link_address", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/b/Clothing-Shoes-Accessories/11450/bn_1852545"}, {"id": 49, "name": "para50_link_text", "desc": "", "type": "string", "exampleValue": "Toys"}, {"id": 50, "name": "para51_link_address", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/b/Toys-Hobbies/220/bn_1865497"}, {"id": 51, "name": "para52_link_text", "desc": "", "type": "string", "exampleValue": "Sporting Goods"}, {"id": 52, "name": "para53_link_address", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/b/Sporting-Goods/888/bn_1865031"}, {"id": 53, "name": "para54_link_text", "desc": "", "type": "string", "exampleValue": "Business & Industrial"}, {"id": 54, "name": "para55_link_address", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/b/Business-Industrial/12576/bn_1853744"}, {"id": 55, "name": "para56_link_text", "desc": "", "type": "string", "exampleValue": "Jewelry & Watches"}, {"id": 56, "name": "para57_link_address", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/b/Jewelry-Watches/281/bn_1865273"}, {"id": 57, "name": "para58_link_text", "desc": "", "type": "string", "exampleValue": "eBay Live"}, {"id": 58, "name": "para59_link_address", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/ebaylive"}, {"id": 59, "name": "para60_link_text", "desc": "", "type": "string", "exampleValue": "Refurbished"}, {"id": 60, "name": "para61_link_address", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/b/eBay-Refurbished/bn_7040708936"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2, 3], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "Open Page", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.ebay.com", "links": "https://www.ebay.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 3, "title": "Collect Data", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 8, "relative": false, "name": "para1_text", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[6]/div[1]/ul[1]/li[1]", "allXPaths": ["/html/body/div[6]/div[1]/ul[1]/li[1]", "//li[contains(., 'Home')]", "//LI[@class='hl-cat-nav__active']"], "exampleValues": [{"num": 0, "value": "Home"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": 3, "index": 3, "parentId": 0, "type": 1, "option": 8, "title": "Loop", "sequence": [4], "isInLoop": false, "position": 2, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[6]/div[1]/ul[1]/li", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[6]/div[1]/ul[1]/li[1]", "//li[contains(., 'Home')]", "//LI[@class='hl-cat-nav__active']"]}}, {"id": 4, "index": 4, "parentId": 3, "type": 0, "option": 3, "title": "Collect Data", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 8, "relative": true, "name": "para2_text", "desc": "", "relativeXPath": "/span[1]", "allXPaths": ["/span[1]", "//span[contains(., 'Home')]"], "exampleValues": [{"num": 0, "value": "Home"}, {"num": 13, "value": "More"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 8, "relative": true, "name": "para3_link_text", "desc": "", "relativeXPath": "/a[1]", "allXPaths": ["/a[1]", "//a[contains(., 'Saved')]"], "exampleValues": [{"num": 1, "value": "Saved"}, {"num": 2, "value": "Motors"}, {"num": 3, "value": "Electronics"}, {"num": 4, "value": "Collectibles"}, {"num": 5, "value": "Home & Garden"}, {"num": 6, "value": "Fashion"}, {"num": 7, "value": "Toys"}, {"num": 8, "value": "Sporting Goods"}, {"num": 9, "value": "Business & Industrial"}, {"num": 10, "value": "Jewelry & Watches"}, {"num": 11, "value": "eBay Live"}, {"num": 12, "value": "Refurbished"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "para4_link_address", "desc": "", "relativeXPath": "/a[1]", "allXPaths": ["/a[1]", "//a[contains(., 'Saved')]"], "exampleValues": [{"num": 1, "value": "https://www.ebay.com/feed"}, {"num": 2, "value": "https://www.ebay.com/b/Auto-Parts-and-Vehicles/6000/bn_1865334"}, {"num": 3, "value": "https://www.ebay.com/b/Electronics/bn_7000259124"}, {"num": 4, "value": "https://www.ebay.com/b/Collectibles-Art/bn_7000259855"}, {"num": 5, "value": "https://www.ebay.com/b/Home-Garden/11700/bn_1853126"}, {"num": 6, "value": "https://www.ebay.com/b/Clothing-Shoes-Accessories/11450/bn_1852545"}, {"num": 7, "value": "https://www.ebay.com/b/Toys-Hobbies/220/bn_1865497"}, {"num": 8, "value": "https://www.ebay.com/b/Sporting-Goods/888/bn_1865031"}, {"num": 9, "value": "https://www.ebay.com/b/Business-Industrial/12576/bn_1853744"}, {"num": 10, "value": "https://www.ebay.com/b/Jewelry-Watches/281/bn_1865273"}, {"num": 11, "value": "https://www.ebay.com/ebaylive"}, {"num": 12, "value": "https://www.ebay.com/b/eBay-Refurbished/bn_7040708936"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "para5_text", "desc": "", "relativeXPath": "/div[1]/button[1]", "allXPaths": ["/div[1]/button[1]", "//button[contains(., 'Expand: Mo')]"], "exampleValues": [{"num": 2, "value": "Expand:Motors"}, {"num": 3, "value": "Expand:Electronics"}, {"num": 4, "value": "Expand:Collectibles"}, {"num": 5, "value": "Expand:Home&Garden"}, {"num": 6, "value": "Expand:Fashion"}, {"num": 7, "value": "Expand:Toys"}, {"num": 8, "value": "Expand:SportingGoods"}, {"num": 9, "value": "Expand:Business&Industrial"}, {"num": 10, "value": "Expand:Jewelry&Watches"}, {"num": 12, "value": "Expand:Refurbished"}, {"num": 13, "value": "Expand:More"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "para6_text", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[1]/h4[1]", "allXPaths": ["/div[2]/div[1]/nav[1]/h4[1]", "//h4[contains(., 'Parts & Ac')]"], "exampleValues": [{"num": 2, "value": "Parts&Accessories"}, {"num": 3, "value": "TopCategories"}, {"num": 4, "value": "TopCategories"}, {"num": 5, "value": "TopCategories"}, {"num": 6, "value": "TopCategories"}, {"num": 7, "value": "TopCategories"}, {"num": 8, "value": "TopCategories"}, {"num": 9, "value": "TopCategories"}, {"num": 10, "value": "TopCategories"}, {"num": 12, "value": "ShopeBayRefurbishedElectronics"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "para7_link_text", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[1]/ul[1]/li[1]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[1]/ul[1]/li[1]/a[1]", "//a[contains(., 'All Parts')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 2, "value": "All Parts & Accessories"}, {"num": 3, "value": "Computers, Tablets & Network Hardware"}, {"num": 4, "value": "Sports Memorabilia, Fan Shop & Sports Cards"}, {"num": 5, "value": "Yard, Garden & Outdoor Living Items"}, {"num": 6, "value": "Women's Clothing"}, {"num": 7, "value": "Action Figures"}, {"num": 8, "value": "Hunting Equipment"}, {"num": 9, "value": "Heavy Equipment"}, {"num": 10, "value": "<PERSON><PERSON><PERSON> Watches"}, {"num": 12, "value": "Cell Phones"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "para8_link_address", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[1]/ul[1]/li[1]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[1]/ul[1]/li[1]/a[1]", "//a[contains(., 'All Parts')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 2, "value": "https://www.ebay.com/b/Auto-Parts-Accessories/6028/bn_569479"}, {"num": 3, "value": "https://www.ebay.com/b/Computers-Tablets-Network-Hardware/58058/bn_1865247"}, {"num": 4, "value": "https://www.ebay.com/b/Sports-Memorabilia-Fan-Shop-Sports-Cards/64482/bn_1857919"}, {"num": 5, "value": "https://www.ebay.com/b/Yard-Garden-Outdoor-Living-Items/159912/bn_1853607"}, {"num": 6, "value": "https://www.ebay.com/b/Womens-Clothing/15724/bn_661783"}, {"num": 7, "value": "https://www.ebay.com/b/Action-Figures-Accessories/246/bn_1648288"}, {"num": 8, "value": "https://www.ebay.com/b/Hunting-Equipment/7301/bn_1865054"}, {"num": 9, "value": "https://www.ebay.com/b/Heavy-Equipment/177641/bn_1511518"}, {"num": 10, "value": "https://www.ebay.com/b/Luxury-Watches/31387/bn_36841947"}, {"num": 12, "value": "https://www.ebay.com/b/Cell-Phones-Smartphones/9355/bn_320094?rt=nc&LH_ItemCondition=2000%7C2010%7C2020%7C2030"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "para9_link_text", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[1]/ul[1]/li[2]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[1]/ul[1]/li[2]/a[1]", "//a[contains(., 'Car & Truc')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 2, "value": "Car & Truck Parts"}, {"num": 3, "value": "Cameras & Photo"}, {"num": 4, "value": "Sports Trading Cards"}, {"num": 5, "value": "Home Improvement"}, {"num": 6, "value": "Women's Shoes"}, {"num": 7, "value": "Dolls & Teddy Bears"}, {"num": 8, "value": "Cycling Equipment"}, {"num": 9, "value": "Personal Protective Equipment"}, {"num": 10, "value": "Wristwatches"}, {"num": 12, "value": "Desktop Computers"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "para10_link_address", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[1]/ul[1]/li[2]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[1]/ul[1]/li[2]/a[1]", "//a[contains(., 'Car & Truc')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 2, "value": "https://www.ebay.com/b/Car-Truck-Parts/6030/bn_562630"}, {"num": 3, "value": "https://www.ebay.com/b/Cameras-Photo/625/bn_1865546"}, {"num": 4, "value": "https://www.ebay.com/b/Sports-Trading-Cards-Accessories/212/bn_1859819"}, {"num": 5, "value": "https://www.ebay.com/b/Home-Improvement/159907/bn_1851980"}, {"num": 6, "value": "https://www.ebay.com/b/Womens-Shoes/3034/bn_740022"}, {"num": 7, "value": "https://www.ebay.com/b/Dolls-Teddy-Bears/237/bn_1865477"}, {"num": 8, "value": "https://www.ebay.com/b/Cycling-Equipment/7294/bn_1848937"}, {"num": 9, "value": "https://www.ebay.com/b/Personal-Protective-Equipment-PPE/183970/bn_78213405"}, {"num": 10, "value": "https://www.ebay.com/b/Wristwatches/31387/bn_2408451"}, {"num": 12, "value": "https://www.ebay.com/b/Desktops-All-In-One-Computers/171957/bn_1643067?rt=nc&mag=1&LH_ItemCondition=2000%7C2010%7C2020%7C2030"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "para11_link_text", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[1]/ul[1]/li[3]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[1]/ul[1]/li[3]/a[1]", "//a[contains(., 'Motorcycle')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 2, "value": "Motorcycle Parts"}, {"num": 3, "value": "Cell Phones & Smartphones"}, {"num": 4, "value": "Coins & Paper Money"}, {"num": 5, "value": "Small Kitchen Appliances"}, {"num": 6, "value": "Women’s Accessories"}, {"num": 7, "value": "Diecast & Toy Vehicles"}, {"num": 8, "value": "Fishing Equipment & Supplies"}, {"num": 9, "value": "Healthcare"}, {"num": 10, "value": "All Watches, Parts, Accessories"}, {"num": 12, "value": "Home Audio"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "para12_link_address", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[1]/ul[1]/li[3]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[1]/ul[1]/li[3]/a[1]", "//a[contains(., 'Motorcycle')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 2, "value": "https://www.ebay.com/b/Motorcycle-Parts/10063/bn_557636"}, {"num": 3, "value": "https://www.ebay.com/b/Cell-Phones-Smartphones/9355/bn_320094"}, {"num": 4, "value": "https://www.ebay.com/b/Coins-Paper-Money/11116/bn_1857806"}, {"num": 5, "value": "https://www.ebay.com/b/Small-Kitchen-Appliances/20667/bn_2311275"}, {"num": 6, "value": "https://www.ebay.com/b/Womens-Accessories/4251/bn_1519247"}, {"num": 7, "value": "https://www.ebay.com/b/Diecast-Toy-Vehicles/222/bn_1850842"}, {"num": 8, "value": "https://www.ebay.com/b/Fishing-Equipment-Supplies/1492/bn_1851047"}, {"num": 9, "value": "https://www.ebay.com/b/Healthcare-Lab-Dental/11815/bn_1851782"}, {"num": 10, "value": "https://www.ebay.com/b/Watches-Parts-Accessories/260324/bn_2408535"}, {"num": 12, "value": "https://www.ebay.com/b/Home-Audio-Equipment/184973/bn_115021122?LH_ItemCondition=2000"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "para13_link_text", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[1]/ul[1]/li[4]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[1]/ul[1]/li[4]/a[1]", "//a[contains(., 'Automotive')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 2, "value": "Automotive Tools & Supplies"}, {"num": 3, "value": "Cell Phone Cases, Covers & Skins"}, {"num": 4, "value": "Antiques"}, {"num": 5, "value": "Lamps, Lighting & Ceiling Fans"}, {"num": 6, "value": "Men's Clothing"}, {"num": 7, "value": "Building Toys"}, {"num": 8, "value": "Golf Clubs"}, {"num": 9, "value": "CNC, Metalworking & Manufacturing"}, {"num": 10, "value": "Fashion Jewelry"}, {"num": 12, "value": "Laptops"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "para14_link_address", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[1]/ul[1]/li[4]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[1]/ul[1]/li[4]/a[1]", "//a[contains(., 'Automotive')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 2, "value": "https://www.ebay.com/b/Automotive-Tools-Supplies/34998/bn_1865501"}, {"num": 3, "value": "https://www.ebay.com/b/Cell-Phone-Cases-Covers-Skins/20349/bn_317585"}, {"num": 4, "value": "https://www.ebay.com/b/Antiques/20081/bn_1851017"}, {"num": 5, "value": "https://www.ebay.com/b/Lamps-Lighting-Ceiling-Fans/20697/bn_818527"}, {"num": 6, "value": "https://www.ebay.com/b/Mens-Clothing/1059/bn_696958"}, {"num": 7, "value": "https://www.ebay.com/b/Building-Toys/183446/bn_1865257"}, {"num": 8, "value": "https://www.ebay.com/b/Golf-Clubs/115280/bn_7244234"}, {"num": 9, "value": "https://www.ebay.com/b/CNC-Metalworking-Manufacturing/11804/bn_1861284"}, {"num": 10, "value": "https://www.ebay.com/b/Fashion-Jewelry/10968/bn_2408529"}, {"num": 12, "value": "https://www.ebay.com/b/Laptops-Netbooks/175672/bn_1648276?rt=nc&mag=1&LH_ItemCondition=2000%7C2010%7C2020%7C2030"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "para15_link_text", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[1]/ul[1]/li[5]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[1]/ul[1]/li[5]/a[1]", "//a[contains(., '<PERSON><PERSON><PERSON> &')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 2, "value": "Apparel & Merchandise"}, {"num": 3, "value": "TV, Video & Home Audio Electronics"}, {"num": 4, "value": "Bullion"}, {"num": 5, "value": "Home Décor"}, {"num": 6, "value": "Men's Shoes"}, {"num": 7, "value": "Collectible Card Games"}, {"num": 8, "value": "Team Sports"}, {"num": 9, "value": "Office"}, {"num": 10, "value": "Fine Jewelry"}, {"num": 12, "value": "Monitors"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "para16_link_address", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[1]/ul[1]/li[5]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[1]/ul[1]/li[5]/a[1]", "//a[contains(., '<PERSON><PERSON><PERSON> &')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 2, "value": "https://www.ebay.com/b/Motors-Apparel-Merchandise/6747/bn_583691"}, {"num": 3, "value": "https://www.ebay.com/b/TV-Video-Home-Audio-Electronics/32852/bn_1648392"}, {"num": 4, "value": "https://www.ebay.com/b/Bullion/39482/bn_1642568"}, {"num": 5, "value": "https://www.ebay.com/b/Home-Decor/10033/bn_1849733"}, {"num": 6, "value": "https://www.ebay.com/b/Mens-Shoes/93427/bn_61999"}, {"num": 7, "value": "https://www.ebay.com/b/Collectible-Card-Games-Accessories/2536/bn_1852210"}, {"num": 8, "value": "https://www.ebay.com/b/Team-Sports/159049/bn_1865097"}, {"num": 9, "value": "https://www.ebay.com/b/Office-Equipment-Supplies/25298/bn_1856567"}, {"num": 10, "value": "https://www.ebay.com/b/Fine-Jewelry/4196/bn_2408477"}, {"num": 12, "value": "https://www.ebay.com/b/Computer-Monitors/80053/bn_317528?LH_ItemCondition=2000"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "para17_link_text", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[1]/ul[1]/li[6]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[1]/ul[1]/li[6]/a[1]", "//a[contains(., 'Motors Dea')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 2, "value": "Motors Deals"}, {"num": 3, "value": "Vehicle Electronics & GPS"}, {"num": 4, "value": "Art"}, {"num": 5, "value": "Power Tools"}, {"num": 6, "value": "Men's Accessories"}, {"num": 7, "value": "Model Railroads & Trains"}, {"num": 8, "value": "Fitness, Running & Yoga Equipment"}, {"num": 9, "value": "Test, Measurement & Inspection Equipment"}, {"num": 10, "value": "Vintage & Antique Jewelry"}, {"num": 12, "value": "Portable Audio & Headphones"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "para18_link_address", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[1]/ul[1]/li[6]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[1]/ul[1]/li[6]/a[1]", "//a[contains(., 'Motors Dea')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 2, "value": "https://www.ebay.com/deals/automotive"}, {"num": 3, "value": "https://www.ebay.com/b/Vehicle-Electronics-GPS/3270/bn_887004"}, {"num": 4, "value": "https://www.ebay.com/b/Art/550/bn_1853728"}, {"num": 5, "value": "https://www.ebay.com/b/Power-Tools/3247/bn_2310272"}, {"num": 6, "value": "https://www.ebay.com/b/Mens-Accessories/4250/bn_1642245"}, {"num": 7, "value": "https://www.ebay.com/b/Model-Trains/180250/bn_1642683"}, {"num": 8, "value": "https://www.ebay.com/b/Fitness-Running-Yoga-Equipment/15273/bn_1855426"}, {"num": 9, "value": "https://www.ebay.com/b/Test-Measurement-Inspection-Equipment/181939/bn_16566063"}, {"num": 10, "value": "https://www.ebay.com/b/Vintage-Antique-Jewelry/262024/bn_16565712"}, {"num": 12, "value": "https://www.ebay.com/b/Portable-Audio-Headphones/15052/bn_1642614?&LH_ItemCondition=2000"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "para19_link_text", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[1]/ul[1]/li[7]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[1]/ul[1]/li[7]/a[1]", "//a[contains(., 'My Garage')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 2, "value": "My Garage"}, {"num": 3, "value": "Headphones"}, {"num": 4, "value": "Collectible Card Games"}, {"num": 5, "value": "Furniture"}, {"num": 6, "value": "Girls' Clothing"}, {"num": 7, "value": "RC Model Vehicles, Toys & Control Line"}, {"num": 8, "value": "Camping & Hiking Equipment"}, {"num": 9, "value": "Restaurant & Food Service"}, {"num": 10, "value": "Loose Diamonds & Gemstones"}, {"num": 12, "value": "Smart Watches"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "para20_link_address", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[1]/ul[1]/li[7]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[1]/ul[1]/li[7]/a[1]", "//a[contains(., 'My Garage')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 2, "value": "https://www.ebay.com/g/mygarage"}, {"num": 3, "value": "https://www.ebay.com/b/Headphones/112529/bn_879608"}, {"num": 4, "value": "https://www.ebay.com/b/Collectible-Card-Games-Accessories/2536/bn_1852210"}, {"num": 5, "value": "https://www.ebay.com/b/Home-Furniture/3197/bn_1642075"}, {"num": 6, "value": "https://www.ebay.com/b/Girls-Clothing-Sizes-4-Up/11462/bn_1650057"}, {"num": 7, "value": "https://www.ebay.com/b/RC-Model-Vehicles-Toys-Control-Line/2562/bn_1851704"}, {"num": 8, "value": "https://www.ebay.com/b/Camping-Hiking-Equipment/16034/bn_1959887"}, {"num": 9, "value": "https://www.ebay.com/b/Restaurant-Food-Service/11874/bn_1865467"}, {"num": 10, "value": "https://www.ebay.com/b/Loose-Diamonds-Gemstones/491/bn_1519324"}, {"num": 12, "value": "https://www.ebay.com/b/Smart-Watches/178893/bn_152365?LH_ItemCondition=2000%7C2010%7C2020%7C2030"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "para21_text", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[2]/h4[1]", "allXPaths": ["/div[2]/div[1]/nav[2]/h4[1]", "//h4[contains(., 'Vehicles')]"], "exampleValues": [{"num": 2, "value": "Vehicles"}, {"num": 3, "value": "PopularTopics"}, {"num": 4, "value": "PopularTopics"}, {"num": 5, "value": "AdditionalCategories"}, {"num": 6, "value": "AdditionalCategories"}, {"num": 7, "value": "PopularTopics"}, {"num": 8, "value": "PopularTopics"}, {"num": 9, "value": "AdditionalCategories"}, {"num": 10, "value": "TopBrands"}, {"num": 12, "value": "ShopeBayRefurbishedHome"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "para22_link_text", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[2]/ul[1]/li[1]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[2]/ul[1]/li[1]/a[1]", "//a[contains(., 'Cars & Tru')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 2, "value": "Cars & Trucks"}, {"num": 3, "value": "eBay Refurbished"}, {"num": 4, "value": "Art Paintings"}, {"num": 5, "value": "eBay Refurbished"}, {"num": 6, "value": "Designer Handbags"}, {"num": 7, "value": "LEGO Sets & Packs"}, {"num": 8, "value": "<PERSON><PERSON>"}, {"num": 9, "value": "Industrial Automation & Motion Control"}, {"num": 10, "value": "Rolex"}, {"num": 12, "value": "Ceiling Fans"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "para23_link_address", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[2]/ul[1]/li[1]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[2]/ul[1]/li[1]/a[1]", "//a[contains(., 'Cars & Tru')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 2, "value": "https://www.ebay.com/b/Cars-and-Trucks/6001/bn_1865117"}, {"num": 3, "value": "https://www.ebay.com/b/eBay-Refurbished/bn_7040708936"}, {"num": 4, "value": "https://www.ebay.com/b/Art-Paintings/551/bn_2310891"}, {"num": 5, "value": "https://www.ebay.com/b/eBay-Refurbished/bn_7040708936"}, {"num": 6, "value": "https://www.ebay.com/b/Designer-Handbags/bn_7117629183"}, {"num": 7, "value": "https://www.ebay.com/b/LEGO-Sets-Packs/19006/bn_1920874"}, {"num": 8, "value": "https://www.ebay.com/b/Billiard-Cues/21568/bn_1943868"}, {"num": 9, "value": "https://www.ebay.com/b/Industrial-Automation-Motion-Controls/42892/bn_2309506"}, {"num": 10, "value": "https://www.ebay.com/b/Rolex-Watches/31387/bn_2989578"}, {"num": 12, "value": "https://www.ebay.com/b/Ceiling-Fans/176937/bn_818516?LH_ItemCondition=2000"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "para24_link_text", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[2]/ul[1]/li[2]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[2]/ul[1]/li[2]/a[1]", "//a[contains(., 'Classics')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 2, "value": "Classics"}, {"num": 3, "value": "Video Games"}, {"num": 4, "value": "Morgan Dollars"}, {"num": 5, "value": "Surveillance & Smart Home"}, {"num": 6, "value": "Collectible Sneakers"}, {"num": 7, "value": "Warhammer 40K"}, {"num": 8, "value": "Camping Meals"}, {"num": 9, "value": "Shipping & Packaging"}, {"num": 10, "value": "OMEGA"}, {"num": 12, "value": "Indoor air quality & Fans"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "para25_link_address", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[2]/ul[1]/li[2]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[2]/ul[1]/li[2]/a[1]", "//a[contains(., 'Classics')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 2, "value": "https://www.ebay.com/b/Classics/bn_7005623268"}, {"num": 3, "value": "https://www.ebay.com/b/Video-Games/139973/bn_320034"}, {"num": 4, "value": "https://www.ebay.com/b/Silver-Morgan-Dollars-1878-1921/39464/bn_26672291"}, {"num": 5, "value": "https://www.ebay.com/b/Surveillance-Smart-Home-Electronics/185067/bn_115028425"}, {"num": 6, "value": "https://www.ebay.com/b/Collectible-Sneakers/bn_7000259435"}, {"num": 7, "value": "https://www.ebay.com/b/Warhammer-40K-Miniatures/183473/bn_1895877"}, {"num": 8, "value": "https://www.ebay.com/b/Camping-Meals/62118/bn_7423940"}, {"num": 9, "value": "https://www.ebay.com/b/Packing-Shipping/19273/bn_1865376"}, {"num": 10, "value": "https://www.ebay.com/b/OMEGA-Watches/31387/bn_3000908"}, {"num": 12, "value": "https://www.ebay.com/b/Indoor-Air-Quality-Fans/185114/bn_115078326?LH_ItemCondition=2000"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "para26_link_text", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[2]/ul[1]/li[3]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[2]/ul[1]/li[3]/a[1]", "//a[contains(., 'Motorcycle')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 2, "value": "Motorcycles"}, {"num": 3, "value": "Nintendo Toys to Life"}, {"num": 4, "value": "Funko Pop!"}, {"num": 5, "value": "Vacuum Cleaners"}, {"num": 6, "value": "Women's Dresses"}, {"num": 7, "value": "Board & Roleplaying Games"}, {"num": 8, "value": "Bikes"}, {"num": 9, "value": "Electrical Equipment & Supplies"}, {"num": 10, "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"num": 12, "value": "Outdoor Power Equipment"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "para27_link_address", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[2]/ul[1]/li[3]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[2]/ul[1]/li[3]/a[1]", "//a[contains(., 'Motorcycle')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 2, "value": "https://www.ebay.com/b/Motorcycles/6024/bn_1865434"}, {"num": 3, "value": "https://www.ebay.com/b/Nintendo-amiibo-Toys-to-Life-Character-Cards/182180/bn_7112334241"}, {"num": 4, "value": "https://www.ebay.com/b/Collectible-Funko-Bobbleheads-1970-Now/149372/bn_3017826"}, {"num": 5, "value": "https://www.ebay.com/b/Vacuum-Cleaners/20614/bn_2310596"}, {"num": 6, "value": "https://www.ebay.com/b/Womens-Dresses/63861/bn_661850"}, {"num": 7, "value": "https://www.ebay.com/b/Games/233/bn_1849806"}, {"num": 8, "value": "https://www.ebay.com/b/Bikes/177831/bn_1865335"}, {"num": 9, "value": "https://www.ebay.com/b/Electrical-Equipment-Supplies/92074/bn_1852224"}, {"num": 10, "value": "https://www.ebay.com/b/Breitling-Watches/31387/bn_3003445"}, {"num": 12, "value": "https://www.ebay.com/b/Outdoor-Power-Equipment/29518/bn_2309717?LH_ItemCondition=2000"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "para28_link_text", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[2]/ul[1]/li[4]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[2]/ul[1]/li[4]/a[1]", "//a[contains(., 'Powersport')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 2, "value": "Powersports"}, {"num": 3, "value": "Apple iPhone"}, {"num": 4, "value": "Disneyana"}, {"num": 5, "value": "KitchenAid Countertop Mixers"}, {"num": 6, "value": "Women's Coats, Jackets & Vests"}, {"num": 7, "value": "Preschool Toys & Pretend Play"}, {"num": 8, "value": "Electric Bikes"}, {"num": 9, "value": "Food Trucks & Concession Trailers"}, {"num": 10, "value": "T<PERSON>"}, {"num": 12, "value": "Power Tools"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "para29_link_address", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[2]/ul[1]/li[4]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[2]/ul[1]/li[4]/a[1]", "//a[contains(., 'Powersport')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 2, "value": "https://www.ebay.com/b/Powersport-Vehicles/66466/bn_1865239"}, {"num": 3, "value": "https://www.ebay.com/b/Apple-Cell-Phones-Smartphones/9355/bn_319682"}, {"num": 4, "value": "https://www.ebay.com/b/Disneyana/137/bn_1855080"}, {"num": 5, "value": "https://www.ebay.com/b/KitchenAid-Countertop-Mixers/133701/bn_2700985"}, {"num": 6, "value": "https://www.ebay.com/b/Womens-Coats-Jackets-Vests/63862/bn_661792"}, {"num": 7, "value": "https://www.ebay.com/b/Preschool-Toys-Pretend-Play/19169/bn_1864380"}, {"num": 8, "value": "https://www.ebay.com/b/Electric-Bikes/74469/bn_1968968"}, {"num": 9, "value": "https://www.ebay.com/b/Food-Trucks-Concession-Trailers/184249/bn_7717994"}, {"num": 10, "value": "https://www.ebay.com/b/TAG-Heuer-Watches/31387/bn_2999599"}, {"num": 12, "value": "https://www.ebay.com/b/Power-Tools/3247/bn_2310272?LH_ItemCondition=2000"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "para30_link_text", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[2]/ul[1]/li[5]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[2]/ul[1]/li[5]/a[1]", "//a[contains(., 'RVs & Camp')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 2, "value": "RVs & Campers"}, {"num": 3, "value": "PC Desktops & All-In-One Computers"}, {"num": 4, "value": "Music Memorabilia"}, {"num": 5, "value": "Outdoor Entertaining"}, {"num": 6, "value": "Women’s Intimates & Sleepwear"}, {"num": 7, "value": "NERF Dart Guns & Soft Darts"}, {"num": 8, "value": "GPS & Running Watches"}, {"num": 9, "value": "Heavy Equipment Attachments"}, {"num": 10, "value": "<PERSON><PERSON>"}, {"num": 12, "value": "Small Kitchen Appliances"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "para31_link_address", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[2]/ul[1]/li[5]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[2]/ul[1]/li[5]/a[1]", "//a[contains(., 'RVs & Camp')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 2, "value": "https://www.ebay.com/b/RVs-and-Campers/50054/bn_16581882"}, {"num": 3, "value": "https://www.ebay.com/b/PC-Desktops-All-In-One-Computers/179/bn_661752"}, {"num": 4, "value": "https://www.ebay.com/b/Music-Memorabilia/2329/bn_1861874"}, {"num": 5, "value": "https://www.ebay.com/b/Patio-Garden-Furniture/25863/bn_2309488"}, {"num": 6, "value": "https://www.ebay.com/b/Womens-Intimates-Sleep/11514/bn_661761"}, {"num": 7, "value": "https://www.ebay.com/b/NERF-Dart-Guns-Soft-Darts/158749/bn_1913487"}, {"num": 8, "value": "https://www.ebay.com/b/GPS-Running-Watches/75230/bn_1970916"}, {"num": 9, "value": "https://www.ebay.com/b/Heavy-Equipment-Attachments/177647/bn_1309146"}, {"num": 10, "value": "https://www.ebay.com/b/<PERSON><PERSON>-<PERSON>-<PERSON>/31387/bn_2975769"}, {"num": 12, "value": "https://www.ebay.com/b/Small-Kitchen-Appliances/20667/bn_2311275_dmd=2&LH_ItemCondition=2000?rt=nc&LH_ItemCondition=2000"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "para32_link_text", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[2]/ul[1]/li[6]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[2]/ul[1]/li[6]/a[1]", "//a[contains(., 'Trailers &')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 2, "value": "Trailers & Other Vehicles"}, {"num": 3, "value": "Computer Graphics Cards"}, {"num": 4, "value": "Baseball Cards"}, {"num": 5, "value": "Bedding"}, {"num": 6, "value": "Men's Shirts"}, {"num": 7, "value": "Reborn Dolls Playsets"}, {"num": 8, "value": "Dumb<PERSON>s"}, {"num": 9, "value": "Heavy Equipment Parts & Accessories"}, {"num": 10, "value": "<PERSON><PERSON>"}, {"num": 12, "value": "Sporting Goods"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "para33_link_address", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[2]/ul[1]/li[6]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[2]/ul[1]/li[6]/a[1]", "//a[contains(., 'Trailers &')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 2, "value": "https://www.ebay.com/b/Other-Vehicles-Trailers/6737/bn_16581863"}, {"num": 3, "value": "https://www.ebay.com/b/Computer-Graphics-Cards/27386/bn_661796"}, {"num": 4, "value": "https://www.ebay.com/b/Baseball-Sports-Trading-Cards-Accessories/212/bn_2309847"}, {"num": 5, "value": "https://www.ebay.com/b/Bedding/20444/bn_1864382"}, {"num": 6, "value": "https://www.ebay.com/b/Mens-Shirts/185100/bn_115044909"}, {"num": 7, "value": "https://www.ebay.com/b/Reborn-Dolls-Playsets/262346/bn_16565603"}, {"num": 8, "value": "https://www.ebay.com/b/Dumbbells/137865/bn_1940974"}, {"num": 9, "value": "https://www.ebay.com/b/Heavy-Equipment-Parts-Accessories/41489/bn_7208228"}, {"num": 10, "value": "https://www.ebay.com/b/Cartier/bn_21821851"}, {"num": 12, "value": "https://www.ebay.com/e/home-garden/certified-refurbished-spoting-goods-hp-flyout"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "para34_link_text", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[2]/ul[1]/li[7]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[2]/ul[1]/li[7]/a[1]", "//a[contains(., 'Boats')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 2, "value": "Boats"}, {"num": 3, "value": "Tablets & eReaders"}, {"num": 4, "value": "NFTs"}, {"num": 5, "value": "Matt<PERSON>"}, {"num": 6, "value": "Men's Coats & Jackets"}, {"num": 7, "value": "Marvel Legends Action Figures"}, {"num": 8, "value": "Shimano Fishing Reels"}, {"num": 9, "value": "Light Industrial Equipment & Tools"}, {"num": 10, "value": "Van Cleef & Arpels"}, {"num": 12, "value": "Surveillance & Smart Home"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "para35_link_address", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[2]/ul[1]/li[7]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[2]/ul[1]/li[7]/a[1]", "//a[contains(., 'Boats')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 2, "value": "https://www.ebay.com/b/Boats/26429/bn_1865510"}, {"num": 3, "value": "https://www.ebay.com/b/Tablets-eReaders/171485/bn_320042"}, {"num": 4, "value": "https://www.ebay.com/b/NFTs-Non-Fungible-Tokens/bn_7117877303"}, {"num": 5, "value": "https://www.ebay.com/b/Mattresses/131588/bn_1519179"}, {"num": 6, "value": "https://www.ebay.com/b/Mens-Coats-Jackets-Vests/57988/bn_704986"}, {"num": 7, "value": "https://www.ebay.com/b/Other-Marvel-Universe-Action-Figures/261068/bn_7116611368?mag=1"}, {"num": 8, "value": "https://www.ebay.com/b/Shimano-Fishing-Reels/261030/bn_1990188"}, {"num": 9, "value": "https://www.ebay.com/b/Light-Industrial-Equipment-Tools/61573/bn_1521576"}, {"num": 10, "value": "https://www.ebay.com/b/Van-<PERSON><PERSON><PERSON>-Arpels-Fine-Jewelry/4196/bn_2194685"}, {"num": 12, "value": "https://www.ebay.com/b/Surveillance-Smart-Home-Electronics/185067/bn_115028425?LH_ItemCondition=2000"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "para36_link_text", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[1]/ul[1]/li[8]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[1]/ul[1]/li[8]/a[1]", "//a[contains(., 'Surveillan')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 3, "value": "Surveillance & Smart Home Electronics"}, {"num": 4, "value": "Comics"}, {"num": 5, "value": "Household & Cleaning Supplies"}, {"num": 6, "value": "Boys' Clothing"}, {"num": 7, "value": "Toy Models & Kits"}, {"num": 8, "value": "Water Sports"}, {"num": 9, "value": "Dental"}, {"num": 10, "value": "All Jewelry"}, {"num": 12, "value": "Tablets"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "para37_link_address", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[1]/ul[1]/li[8]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[1]/ul[1]/li[8]/a[1]", "//a[contains(., 'Surveillan')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 3, "value": "https://www.ebay.com/b/Surveillance-Smart-Home-Electronics/185067/bn_115028425"}, {"num": 4, "value": "https://www.ebay.com/b/Comic-Books-Manga-Memorabilia/63/bn_1865459"}, {"num": 5, "value": "https://www.ebay.com/b/Household-Cleaning-Supplies/299/bn_1857080"}, {"num": 6, "value": "https://www.ebay.com/b/Boys-Clothing-Sizes-4-Up/11452/bn_661760"}, {"num": 7, "value": "https://www.ebay.com/b/Toy-Models-Kits/1188/bn_1852447"}, {"num": 8, "value": "https://www.ebay.com/b/Water-Sports/159136/bn_1855031"}, {"num": 9, "value": "https://www.ebay.com/b/Dental/bn_7115058704"}, {"num": 10, "value": "https://www.ebay.com/b/Jewelry/bn_7000259126"}, {"num": 12, "value": "https://www.ebay.com/b/Tablets-eReaders/171485/bn_320042?rt=nc&mag=1&LH_ItemCondition=2000%7C2010%7C2020%7C2030"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "para38_link_text", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[2]/ul[1]/li[8]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[2]/ul[1]/li[8]/a[1]", "//a[contains(., 'Laptops &')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 3, "value": "Laptops & Netbooks"}, {"num": 4, "value": "Stamps"}, {"num": 5, "value": "Home & Garden Deals"}, {"num": 6, "value": "Fashion Deals"}, {"num": 7, "value": "Toys Deals"}, {"num": 8, "value": "<PERSON><PERSON> Golf Clubs"}, {"num": 9, "value": "HVAC & Refrigeration"}, {"num": 10, "value": "Tiffany & Co."}, {"num": 12, "value": "Vacuums"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "para39_link_address", "desc": "", "relativeXPath": "/div[2]/div[1]/nav[2]/ul[1]/li[8]/a[1]", "allXPaths": ["/div[2]/div[1]/nav[2]/ul[1]/li[8]/a[1]", "//a[contains(., 'Laptops &')]", "//A[@class='hl-cat-nav__js-link']"], "exampleValues": [{"num": 3, "value": "https://www.ebay.com/b/Laptops-Netbooks/175672/bn_1648276"}, {"num": 4, "value": "https://www.ebay.com/b/Stamps/260/bn_1865095"}, {"num": 5, "value": "https://www.ebay.com/deals/home-garden"}, {"num": 6, "value": "https://www.ebay.com/deals/fashion"}, {"num": 7, "value": "https://www.ebay.com/deals/other-deals/toys"}, {"num": 8, "value": "https://www.ebay.com/b/<PERSON><PERSON>-<PERSON>-Golf-Clubs/115280/bn_7249420"}, {"num": 9, "value": "https://www.ebay.com/b/HVAC-Refrigeration/42909/bn_2312341"}, {"num": 10, "value": "https://www.ebay.com/b/Tiffany-Co-Fine-Jewelry/4196/bn_2194322"}, {"num": 12, "value": "https://www.ebay.com/b/Vacuum-Cleaners/20614/bn_2310596?LH_ItemCondition=2000"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "para40_link_text", "desc": "", "relativeXPath": "/div[2]/a[1]", "allXPaths": ["/div[2]/a[1]", "//a[contains(., 'Motors')]"], "exampleValues": [{"num": 13, "value": "Motors"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "para41_link_address", "desc": "", "relativeXPath": "/div[2]/a[1]", "allXPaths": ["/div[2]/a[1]", "//a[contains(., 'Motors')]"], "exampleValues": [{"num": 13, "value": "https://www.ebay.com/b/Auto-Parts-and-Vehicles/6000/bn_1865334"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "para42_link_text", "desc": "", "relativeXPath": "/div[2]/a[2]", "allXPaths": ["/div[2]/a[2]", "//a[contains(., 'Electronic')]"], "exampleValues": [{"num": 13, "value": "Electronics"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "para43_link_address", "desc": "", "relativeXPath": "/div[2]/a[2]", "allXPaths": ["/div[2]/a[2]", "//a[contains(., 'Electronic')]"], "exampleValues": [{"num": 13, "value": "https://www.ebay.com/b/Electronics/bn_7000259124"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "para44_link_text", "desc": "", "relativeXPath": "/div[2]/a[3]", "allXPaths": ["/div[2]/a[3]", "//a[contains(., 'Collectibl')]"], "exampleValues": [{"num": 13, "value": "Collectibles"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "para45_link_address", "desc": "", "relativeXPath": "/div[2]/a[3]", "allXPaths": ["/div[2]/a[3]", "//a[contains(., 'Collectibl')]"], "exampleValues": [{"num": 13, "value": "https://www.ebay.com/b/Collectibles-Art/bn_7000259855"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "para46_link_text", "desc": "", "relativeXPath": "/div[2]/a[4]", "allXPaths": ["/div[2]/a[4]", "//a[contains(., 'Home & Gar')]"], "exampleValues": [{"num": 13, "value": "Home & Garden"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "para47_link_address", "desc": "", "relativeXPath": "/div[2]/a[4]", "allXPaths": ["/div[2]/a[4]", "//a[contains(., 'Home & Gar')]"], "exampleValues": [{"num": 13, "value": "https://www.ebay.com/b/Home-Garden/11700/bn_1853126"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "para48_link_text", "desc": "", "relativeXPath": "/div[2]/a[5]", "allXPaths": ["/div[2]/a[5]", "//a[contains(., 'Fashion')]"], "exampleValues": [{"num": 13, "value": "Fashion"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "para49_link_address", "desc": "", "relativeXPath": "/div[2]/a[5]", "allXPaths": ["/div[2]/a[5]", "//a[contains(., 'Fashion')]"], "exampleValues": [{"num": 13, "value": "https://www.ebay.com/b/Clothing-Shoes-Accessories/11450/bn_1852545"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "para50_link_text", "desc": "", "relativeXPath": "/div[2]/a[6]", "allXPaths": ["/div[2]/a[6]", "//a[contains(., 'Toys')]"], "exampleValues": [{"num": 13, "value": "Toys"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "para51_link_address", "desc": "", "relativeXPath": "/div[2]/a[6]", "allXPaths": ["/div[2]/a[6]", "//a[contains(., 'Toys')]"], "exampleValues": [{"num": 13, "value": "https://www.ebay.com/b/Toys-Hobbies/220/bn_1865497"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "para52_link_text", "desc": "", "relativeXPath": "/div[2]/a[7]", "allXPaths": ["/div[2]/a[7]", "//a[contains(., 'Sporting G')]"], "exampleValues": [{"num": 13, "value": "Sporting Goods"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "para53_link_address", "desc": "", "relativeXPath": "/div[2]/a[7]", "allXPaths": ["/div[2]/a[7]", "//a[contains(., 'Sporting G')]"], "exampleValues": [{"num": 13, "value": "https://www.ebay.com/b/Sporting-Goods/888/bn_1865031"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "para54_link_text", "desc": "", "relativeXPath": "/div[2]/a[8]", "allXPaths": ["/div[2]/a[8]", "//a[contains(., 'Business &')]"], "exampleValues": [{"num": 13, "value": "Business & Industrial"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "para55_link_address", "desc": "", "relativeXPath": "/div[2]/a[8]", "allXPaths": ["/div[2]/a[8]", "//a[contains(., 'Business &')]"], "exampleValues": [{"num": 13, "value": "https://www.ebay.com/b/Business-Industrial/12576/bn_1853744"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "para56_link_text", "desc": "", "relativeXPath": "/div[2]/a[9]", "allXPaths": ["/div[2]/a[9]", "//a[contains(., 'Jewelry &')]"], "exampleValues": [{"num": 13, "value": "Jewelry & Watches"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "para57_link_address", "desc": "", "relativeXPath": "/div[2]/a[9]", "allXPaths": ["/div[2]/a[9]", "//a[contains(., 'Jewelry &')]"], "exampleValues": [{"num": 13, "value": "https://www.ebay.com/b/Jewelry-Watches/281/bn_1865273"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "para58_link_text", "desc": "", "relativeXPath": "/div[2]/a[10]", "allXPaths": ["/div[2]/a[10]", "//a[contains(., 'eBay Live')]"], "exampleValues": [{"num": 13, "value": "eBay Live"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "para59_link_address", "desc": "", "relativeXPath": "/div[2]/a[10]", "allXPaths": ["/div[2]/a[10]", "//a[contains(., 'eBay Live')]"], "exampleValues": [{"num": 13, "value": "https://www.ebay.com/ebaylive"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "para60_link_text", "desc": "", "relativeXPath": "/div[2]/a[11]", "allXPaths": ["/div[2]/a[11]", "//a[contains(., 'Refurbishe')]"], "exampleValues": [{"num": 13, "value": "Refurbished"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "para61_link_address", "desc": "", "relativeXPath": "/div[2]/a[11]", "allXPaths": ["/div[2]/a[11]", "//a[contains(., 'Refurbishe')]"], "exampleValues": [{"num": 13, "value": "https://www.ebay.com/b/eBay-Refurbished/bn_7040708936"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}]}