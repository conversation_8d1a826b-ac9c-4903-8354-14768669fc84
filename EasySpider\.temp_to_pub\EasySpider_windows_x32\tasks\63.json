{"id": 63, "name": "地震台网", "url": "http://www.ceic.ac.cn/history", "links": "http://www.ceic.ac.cn/history\nhttp://www.ceic.ac.cn/history2", "create_time": "7/7/2023, 2:15:59 AM", "version": "0.3.5", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "outputFormat": "xlsx", "saveName": "地震信息", "containJudge": false, "desc": "http://www.ceic.ac.cn/history", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "http://www.ceic.ac.cn/history\nhttp://www.ceic.ac.cn/history2", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "http://www.ceic.ac.cn/history\nhttp://www.ceic.ac.cn/history2"}, {"id": 1, "name": "loopText_1", "nodeId": 2, "nodeName": "循环", "desc": "要输入的文本/网址,多行以\\n分开", "type": "string", "exampleValue": "1\n2\n3\n4", "value": "1\n2\n3\n4"}, {"id": 2, "name": "loopTimes_循环_2", "nodeId": 5, "nodeName": "循环", "desc": "循环循环执行的次数（0代表无限循环）", "type": "int", "exampleValue": 2, "value": 2}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "type": "string", "exampleValue": "3.12023-05-09 14:12:2241.0778.8410新疆阿克苏地区乌什县"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "http://www.ceic.ac.cn/history", "links": "http://www.ceic.ac.cn/history\nhttp://www.ceic.ac.cn/history2", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 0, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [3, 4, 5], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 0, "loopType": "3", "pathList": "", "textList": "1\n2\n3\n4", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": "1", "breakCode": "return window.innerHeight > 500", "breakCodeWaitTime": 0}}, {"id": 3, "index": 3, "parentId": 2, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": 0, "useLoop": true, "xpath": "//*[@id=\"weidu1\"]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "1", "allXPaths": ["/html/body/div[1]/div[3]/div[1]/div[1]/div[1]/form[1]/div[2]/input[1]", "//input[contains(., '')]", "id(\"weidu1\")", "//INPUT[@class='span1']", "//INPUT[@name='weidu1']"]}}, {"id": 4, "index": 4, "parentId": 2, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 4, "tabIndex": 0, "useLoop": false, "xpath": "//*[@id=\"search\"]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[1]/div[3]/div[1]/div[1]/div[1]/form[1]/div[5]/a[1]", "//a[contains(., '查询')]", "id(\"search\")", "//A[@class='check']"]}}, {"id": 5, "index": 5, "parentId": 2, "type": 1, "option": 8, "title": "循环", "sequence": [7, 6], "isInLoop": true, "position": 2, "parameters": {"history": 4, "tabIndex": 0, "useLoop": false, "xpath": "//a[contains(., '»')]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 0, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 2, "historyWait": 2, "breakMode": "1", "breakCode": "return window.innerWidth > 500", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[2]/div[1]/div[1]/ul[1]/li[10]/a[1]", "//a[contains(., '»')]"]}}, {"id": 7, "index": 6, "parentId": 5, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 4, "tabIndex": 0, "useLoop": true, "xpath": "//*[contains(@class, \"pagination\")]/ul[1]/li[10]/a[1]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[2]/div[1]/div[1]/ul[1]/li[10]/a[1]", "//a[contains(., '»')]"], "loopType": 0}}, {"id": 6, "index": 7, "parentId": 5, "type": 1, "option": 8, "title": "循环", "sequence": [8], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": 0, "useLoop": false, "xpath": "/html/body/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[1]/table[1]/tbody[1]/tr", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 0, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[1]/table[1]/tbody[1]/tr[1]", "//tr[contains(., '震级(M)发震时刻(')]", "//TR[@class='speed-tr-h1']"]}}, {"id": 8, "index": 8, "parentId": 6, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "3.12023-05-09 14:12:2241.0778.8410新疆阿克苏地区乌什县"}, {"num": 1, "value": "震级(M)发震时刻(UTC+8)纬度(°)经度(°)深度(千米)参考位置"}, {"num": 2, "value": "2.32023-05-09 12:31:4637.51115.0710河北邢台市宁晋县"}, {"num": 3, "value": "3.02023-05-08 04:06:4629.72103.2618四川眉山市洪雅县"}, {"num": 4, "value": "3.32023-05-06 21:23:4529.61102.038四川甘孜州泸定县"}, {"num": 5, "value": "3.02023-05-06 20:39:1130.67117.0510安徽安庆市宜秀区"}, {"num": 6, "value": "3.72023-05-06 13:01:5729.15105.4510四川泸州市泸县"}, {"num": 7, "value": "3.12023-05-06 10:19:4347.73130.858黑龙江鹤岗市萝北县"}, {"num": 8, "value": "5.42023-05-06 01:47:1741.45142.1040日本北海道地区"}, {"num": 9, "value": "5.72023-05-05 20:58:0537.35137.3510日本本州西岸近海"}, {"num": 10, "value": "4.32023-05-05 20:33:1928.08105.128四川宜宾市兴文县"}, {"num": 11, "value": "6.32023-05-05 13:42:0537.40137.4010日本本州西岸近海"}, {"num": 12, "value": "3.12023-05-05 11:32:0425.1298.1312云南德宏州盈江县"}, {"num": 13, "value": "3.22023-05-05 10:28:3925.1498.118云南德宏州盈江县"}, {"num": 14, "value": "3.42023-05-05 08:35:2635.7979.8610新疆和田地区和田县"}, {"num": 15, "value": "4.52023-05-04 23:38:2123.46121.3310台湾花莲县"}, {"num": 16, "value": "2.12023-05-04 19:00:3138.46117.979河北沧州市黄骅市海域"}, {"num": 17, "value": "3.02023-05-04 15:53:4640.8083.9115新疆阿克苏地区沙雅县"}, {"num": 18, "value": "3.32023-05-04 14:06:2428.92102.4715四川雅安市石棉县"}, {"num": 19, "value": "3.32023-05-04 11:46:2433.34119.2814江苏扬州市宝应县"}, {"num": 20, "value": "4.92023-05-04 10:15:2828.14105.1210四川宜宾市兴文县"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}, {"id": -1, "index": 9, "parentId": 2, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": true, "position": 3, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": ""}}]}